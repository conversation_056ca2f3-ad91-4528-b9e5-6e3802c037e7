-- Fix user_profiles table to add missing subscription_status column
-- This addresses the schema mismatch where code references subscription_status but column doesn't exist

-- Add subscription_status column if it doesn't exist
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS subscription_status TEXT DEFAULT 'inactive' 
CHECK (subscription_status IN ('active', 'inactive', 'canceled', 'past_due', 'unpaid', 'trialing', 'incomplete'));

-- Update any existing users to have inactive status by default
UPDATE public.user_profiles 
SET subscription_status = 'inactive' 
WHERE subscription_status IS NULL;

-- Add comment to document the column
COMMENT ON COLUMN public.user_profiles.subscription_status IS 'Current subscription status - synced with Stripe subscription status';

-- Create index for performance on subscription status queries
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription_status 
ON public.user_profiles(subscription_status);

-- Update RLS policies to include subscription_status in user profile policies
-- (The existing policies should already cover this, but let's be explicit)

-- Ensure users can view their subscription status
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
CREATE POLICY "Users can view their own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

-- Ensure users can update their own profile (but subscription_status should only be updated by webhooks)
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
CREATE POLICY "Users can update their own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- Service role can manage all profiles (for webhook updates)
CREATE POLICY IF NOT EXISTS "Service role can manage all user profiles" ON public.user_profiles
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
