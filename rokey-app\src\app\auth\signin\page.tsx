'use client';

import { useState, Suspense, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter, useSearchParams } from 'next/navigation';

function SignInPageContent() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createClientComponentClient();

  useEffect(() => {
    // Check if user is already signed in
    const checkUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        const redirectTo = searchParams.get('redirectTo');
        const plan = searchParams.get('plan');

        if (plan && ['starter', 'professional', 'enterprise'].includes(plan)) {
          router.push(`/pricing?plan=${plan}&checkout=true`);
        } else if (redirectTo) {
          router.push(redirectTo);
        } else {
          router.push('/dashboard');
        }
      }
    };
    checkUser();
  }, [router, searchParams, supabase.auth]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        // Check subscription status before redirecting
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('subscription_status, subscription_tier')
          .eq('id', data.user.id)
          .single();

        const redirectTo = searchParams.get('redirectTo');
        const plan = searchParams.get('plan');

        // Check if this is a checkout flow for a pending user
        const checkoutUserId = searchParams.get('checkout_user_id');

        // If user doesn't have active subscription, redirect appropriately
        if (!profile || profile.subscription_status !== 'active') {
          // If this is a checkout flow, redirect to checkout
          if (checkoutUserId && plan && ['starter', 'professional', 'enterprise'].includes(plan)) {
            router.push(`/checkout?plan=${plan}&user_id=${data.user.id}`);
          } else if (plan && ['starter', 'professional', 'enterprise'].includes(plan)) {
            router.push(`/pricing?plan=${plan}&checkout=true&message=subscription_required`);
          } else {
            router.push('/pricing?checkout=true&message=subscription_required');
          }
          return;
        }

        // User has active subscription, proceed with normal redirect
        if (plan && ['starter', 'professional', 'enterprise'].includes(plan)) {
          router.push(`/pricing?plan=${plan}&checkout=true`);
        } else if (redirectTo) {
          router.push(redirectTo);
        } else {
          router.push('/dashboard');
        }
      }
    } catch (err: any) {
      console.error('Sign in error:', err);
      setError(err.message || 'Invalid email or password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      const plan = searchParams.get('plan');
      const redirectTo = searchParams.get('redirectTo');

      let finalRedirect = '/dashboard';
      if (plan && ['starter', 'professional', 'enterprise'].includes(plan)) {
        finalRedirect = `/pricing?plan=${plan}&checkout=true`;
      } else if (redirectTo) {
        finalRedirect = redirectTo;
      }

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback?redirectTo=${encodeURIComponent(finalRedirect)}`,
        },
      });

      if (error) {
        throw error;
      }

      // OAuth redirect will handle the rest
    } catch (err: any) {
      console.error('Google sign in error:', err);
      setError(err.message || 'Failed to sign in with Google. Please try again.');
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      {/* Grid Background */}
      <EnhancedGridBackground
        gridSize={50}
        opacity={0.064}
        color="#000000"
        variant="subtle"
        animated={true}
        className="fixed inset-0"
      />

      {/* Orange Accent Grid */}
      <div className="absolute inset-0">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)`,
            backgroundSize: '100px 100px',
            mask: `radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)`,
            WebkitMask: `radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)`
          }}
        ></div>
      </div>

      <div className="relative z-10 w-full max-w-6xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Branding */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="hidden lg:block"
          >
            <div className="bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden">
              {/* Background Pattern */}
              <div
                className="absolute inset-0 opacity-20"
                style={{
                  backgroundImage: `
                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                  `,
                  backgroundSize: '30px 30px'
                }}
              ></div>

              <div className="relative z-10">
                <div className="mb-8">
                  <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm">
                    <Image
                      src="/roukey_logo.png"
                      alt="RouKey"
                      width={48}
                      height={48}
                      className="w-12 h-12 object-contain"
                      priority
                    />
                  </div>
                  <h1 className="text-4xl font-bold mb-4">Welcome to RouKey</h1>
                  <p className="text-xl text-white/90 mb-8">
                    Access to <span className="font-bold">UNLIMITED</span> AI requests across 300+ models
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                    <span className="text-white/90">Intelligent Role Routing</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                    <span className="text-white/90">Enterprise Security</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                    <span className="text-white/90">No Request Limits</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Right Side - Form */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="w-full max-w-md mx-auto lg:mx-0"
          >
            {/* Header */}
            <div className="text-center mb-8">
              <Link href="/" className="inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start">
                <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1">
                  <Image
                    src="/roukey_logo.png"
                    alt="RouKey"
                    width={40}
                    height={40}
                    className="w-full h-full object-contain"
                    priority
                  />
                </div>
                <span className="text-3xl font-bold text-black">RouKey</span>
              </Link>

              <h2 className="text-4xl font-bold text-black mb-3">Sign In</h2>
              <p className="text-gray-600 text-lg">Welcome back to your AI gateway</p>
            </div>

            {/* Enhanced Form */}
            <div className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden">
              {/* Form Background Pattern */}
              <div
                className="absolute inset-0 opacity-5"
                style={{
                  backgroundImage: `
                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                  `,
                  backgroundSize: '20px 20px'
                }}
              ></div>
              <div className="relative z-10">
                {/* Success message for account creation */}
                {searchParams.get('message') === 'account_created' && (
                  <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
                    <p className="text-green-600 text-sm font-medium">
                      ✅ Account created successfully! Please sign in to complete your checkout.
                    </p>
                  </div>
                )}

                {error && (
                  <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                    <p className="text-red-600 text-sm">{error}</p>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-semibold text-gray-800 mb-3">
                      📧 Email Address
                    </label>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white"
                      placeholder="Enter your email"
                    />
                  </div>

                  <div>
                    <label htmlFor="password" className="block text-sm font-semibold text-gray-800 mb-3">
                      🔒 Password
                    </label>
                    <div className="relative">
                      <input
                        id="password"
                        name="password"
                        type={showPassword ? 'text' : 'password'}
                        autoComplete="current-password"
                        required
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white"
                        placeholder="Enter your password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors"
                      >
                        {showPassword ? (
                          <EyeSlashIcon className="h-5 w-5" />
                        ) : (
                          <EyeIcon className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        id="remember-me"
                        name="remember-me"
                        type="checkbox"
                        className="h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg"
                      />
                      <label htmlFor="remember-me" className="ml-3 block text-sm font-medium text-gray-700">
                        Remember me
                      </label>
                    </div>

                    <Link
                      href="/auth/reset-password"
                      className="text-sm text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors"
                    >
                      Forgot password?
                    </Link>
                  </div>

                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Signing in...
                      </div>
                    ) : (
                      'Sign In'
                    )}
                  </button>
                </form>

                {/* Divider */}
                <div className="mt-8">
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-200" />
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-4 bg-white text-gray-500 font-medium">Or continue with</span>
                    </div>
                  </div>
                </div>

                {/* Google Sign In */}
                <button
                  onClick={handleGoogleSignIn}
                  disabled={isLoading}
                  className="mt-6 w-full bg-white border-2 border-gray-200 text-gray-700 py-4 px-6 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-sm hover:shadow-md"
                >
                  <svg className="w-6 h-6 mr-3" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Continue with Google
                </button>
              </div>
            </div>

            {/* Sign Up Link */}
            <div className="text-center mt-8">
              <p className="text-gray-600 text-lg">
                Don't have an account?{' '}
                <Link href="/auth/signup" className="text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors">
                  Sign up for free
                </Link>
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

export default function SignInPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <SignInPageContent />
    </Suspense>
  );
}
