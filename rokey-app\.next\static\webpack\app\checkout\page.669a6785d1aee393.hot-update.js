"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkout/page",{

/***/ "(app-pages-browser)/./src/app/checkout/page.tsx":
/*!***********************************!*\
  !*** ./src/app/checkout/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CheckoutPageContent() {\n    console.log('🔥 CheckoutPageContent function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActualCheckoutContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n_c = CheckoutPageContent;\nfunction ActualCheckoutContent() {\n    _s();\n    // Immediate debug log\n    console.log('🔥 ActualCheckoutContent function called');\n    // React hooks must be at the top level\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__.createClientComponentClient)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    console.log('🔥 ActualCheckoutContent - all hooks initialized');\n    const selectedPlan = searchParams.get('plan') || 'professional';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    const isSignup = searchParams.get('signup') === 'true';\n    // Debug the URL params immediately\n    console.log('🔍 ActualCheckoutContent URL params parsed:', {\n        selectedPlan,\n        userId,\n        email,\n        isSignup\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent first useEffect - component mounting...');\n            fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'COMPONENT_MOUNT',\n                    message: 'ActualCheckoutContent component is mounting'\n                })\n            }).catch({\n                \"ActualCheckoutContent.useEffect\": ()=>{}\n            }[\"ActualCheckoutContent.useEffect\"]);\n            console.log('🚀 ActualCheckoutContent - setting mounted to true');\n            setMounted(true);\n        }\n    }[\"ActualCheckoutContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent second useEffect - mounted:', mounted);\n            if (!mounted) {\n                console.log('🚀 ActualCheckoutContent - not mounted yet, returning');\n                return;\n            }\n            console.log('=== CHECKOUT PAGE MOUNTED ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            console.log('Current URL:', window.location.href);\n            // Check localStorage for pending signup\n            const pendingSignup = localStorage.getItem('pending_signup');\n            console.log('localStorage pending_signup:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n            if (pendingSignup) {\n                console.log('Pending signup data:', JSON.parse(pendingSignup));\n            }\n            // Add debug function to window for manual testing\n            window.debugCheckout = ({\n                \"ActualCheckoutContent.useEffect\": ()=>{\n                    console.log('=== DEBUG CHECKOUT ===');\n                    console.log('localStorage pending_signup:', localStorage.getItem('pending_signup'));\n                    console.log('URL params:', {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    });\n                }\n            })[\"ActualCheckoutContent.useEffect\"];\n            initializeCheckout();\n        }\n    }[\"ActualCheckoutContent.useEffect\"], [\n        mounted\n    ]);\n    const initializeCheckout = async ()=>{\n        try {\n            var _session_user, _session_user1;\n            // Send debug info to server for terminal logging\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CHECKOUT_INITIALIZATION',\n                    urlParams: {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    },\n                    currentUrl: window.location.href\n                })\n            }).catch(()=>{}); // Don't fail if debug endpoint fails\n            console.log('=== CHECKOUT INITIALIZATION ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            // Get current user session\n            const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n            console.log('Session check:', {\n                hasSession: !!session,\n                error: sessionError\n            });\n            // Debug session details\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'SESSION_CHECK',\n                    hasSession: !!session,\n                    sessionError: sessionError === null || sessionError === void 0 ? void 0 : sessionError.message,\n                    userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                    userEmail: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email\n                })\n            }).catch(()=>{});\n            if (sessionError || !session) {\n                console.log('No valid session found');\n                console.log('Session error:', sessionError);\n                console.log('Session data:', session);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'SESSION_FAILED',\n                        error: (sessionError === null || sessionError === void 0 ? void 0 : sessionError.message) || 'No session found',\n                        userId: userId,\n                        hasUserId: !!userId,\n                        redirecting: true\n                    })\n                }).catch(()=>{});\n                // If we have a userId from URL params, this means user was created but not signed in\n                // We need to prompt them to sign in with their credentials\n                if (userId) {\n                    setError('Please sign in with your account credentials to complete checkout.');\n                    setTimeout(()=>router.push(\"/auth/signin?plan=\".concat(selectedPlan, \"&checkout_user_id=\").concat(userId)), 3000);\n                } else {\n                    setError('Authentication required. Please sign up first.');\n                    setTimeout(()=>router.push(\"/auth/signup?plan=\".concat(selectedPlan)), 3000);\n                }\n                return;\n            }\n            setUser(session.user);\n            console.log('Set user state:', session.user);\n            // Check if user has payment_pending status (new signup)\n            const userMetadata = session.user.user_metadata;\n            const paymentStatus = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.payment_status;\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'USER_SESSION_FOUND',\n                    userId: session.user.id,\n                    email: session.user.email,\n                    paymentStatus,\n                    userMetadata\n                })\n            }).catch(()=>{});\n            console.log('Processing checkout for user:', session.user.id);\n            console.log('Payment status:', paymentStatus);\n            // Debug before calling createCheckoutSession\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CALLING_CREATE_CHECKOUT_SESSION',\n                    userId: session.user.id,\n                    selectedPlan,\n                    aboutToCall: true\n                })\n            }).catch(()=>{});\n            console.log('About to call createCheckoutSession...');\n            try {\n                // Create checkout session for authenticated user\n                // Pass the session user directly instead of relying on state\n                await createCheckoutSession(session.user.id, session.user);\n                console.log('createCheckoutSession call completed successfully');\n            } catch (checkoutError) {\n                console.error('Error in createCheckoutSession:', checkoutError);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'CREATE_CHECKOUT_SESSION_ERROR',\n                        error: checkoutError.message,\n                        stack: checkoutError.stack\n                    })\n                }).catch(()=>{});\n                throw checkoutError; // Re-throw to be caught by outer catch\n            }\n        } catch (error) {\n            console.error('Checkout initialization error:', error);\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'ERROR',\n                    error: error.message\n                })\n            }).catch(()=>{});\n            setError('Failed to initialize checkout. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createCheckoutSessionForSignup = async (userData)=>{\n        try {\n            const requestData = {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userEmail: userData.email,\n                signup: true,\n                pendingUserData: userData\n            };\n            console.log('Creating checkout session for signup:', requestData);\n            console.log('Environment check:', {\n                hasStarterPrice: !!\"price_1RaA5xC97XFBBUvdt12n1i0T\",\n                hasProfessionalPrice: !!\"price_1RaABVC97XFBBUvdkZZc1oQB\",\n                hasEnterprisePrice: !!\"price_1RaADDC97XFBBUvd7j6OPJj7\"\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const data = await response.json();\n            console.log('Checkout session response:', {\n                ok: response.ok,\n                status: response.status,\n                data\n            });\n            if (!response.ok) {\n                console.error('Checkout session creation failed:', data);\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Signup checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const createCheckoutSession = async (userId, sessionUser)=>{\n        console.log('🚀 createCheckoutSession function called with userId:', userId);\n        try {\n            var _currentUser_user_metadata, _currentUser_user_metadata1;\n            // Use passed sessionUser or fallback to state user\n            const currentUser = sessionUser || user;\n            // Get email from multiple sources\n            const userEmail = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata = currentUser.user_metadata) === null || _currentUser_user_metadata === void 0 ? void 0 : _currentUser_user_metadata.email) || email;\n            console.log('Email extraction debug:', {\n                currentUserEmail: currentUser === null || currentUser === void 0 ? void 0 : currentUser.email,\n                currentUserMetadataEmail: currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata1 = currentUser.user_metadata) === null || _currentUser_user_metadata1 === void 0 ? void 0 : _currentUser_user_metadata1.email,\n                urlEmail: email,\n                finalEmail: userEmail,\n                currentUserObject: currentUser\n            });\n            if (!userEmail) {\n                console.error('No email found in any source:', {\n                    currentUser,\n                    stateUser: user,\n                    urlEmail: email\n                });\n                throw new Error('User email not found');\n            }\n            console.log('Creating checkout session with:', {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userId: userId,\n                userEmail: userEmail\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId: getPriceId(selectedPlan),\n                    tier: selectedPlan,\n                    userId: userId,\n                    userEmail: userEmail,\n                    signup: false\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const getPriceId = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return \"price_1RaA5xC97XFBBUvdt12n1i0T\" || 0;\n            case 'professional':\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n            case 'enterprise':\n                return \"price_1RaADDC97XFBBUvd7j6OPJj7\" || 0;\n            default:\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n        }\n    };\n    const getPlanPrice = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return '$29';\n            case 'professional':\n                return '$99';\n            case 'enterprise':\n                return '$299';\n            default:\n                return '$99';\n        }\n    };\n    // Show loading until component is mounted (prevents hydration issues)\n    if (!mounted) {\n        console.log('⏳ Component not mounted yet, showing loading...');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 360,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 359,\n            columnNumber: 7\n        }, this);\n    }\n    console.log('✅ Component mounted, proceeding with render...');\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Checkout Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                        href: \"/auth/signup?plan=\".concat(selectedPlan),\n                        className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#e55a2b] transition-colors\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 373,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 372,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/roukey_logo.png\",\n                            alt: \"RouKey\",\n                            width: 40,\n                            height: 40,\n                            className: \"w-10 h-10 object-contain\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Setting up your subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#ff6b35] font-semibold\",\n                                            children: [\n                                                selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1),\n                                                \" Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        getPlanPrice(selectedPlan),\n                                        \"/month\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"You'll be redirected to Stripe to complete your payment securely.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"After payment, you'll verify your email and gain access to your dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex items-center justify-center space-x-2 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Secured by Stripe\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 394,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 393,\n        columnNumber: 5\n    }, this);\n}\n_s(ActualCheckoutContent, \"28X6dCLSY33yGsWYE1GDYj1wduQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c1 = ActualCheckoutContent;\nfunction CheckoutPage() {\n    console.log('🚀 CheckoutPage main function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckoutPageContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 449,\n        columnNumber: 10\n    }, this);\n}\n_c2 = CheckoutPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CheckoutPageContent\");\n$RefreshReg$(_c1, \"ActualCheckoutContent\");\n$RefreshReg$(_c2, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/checkout/page.tsx\n"));

/***/ })

});