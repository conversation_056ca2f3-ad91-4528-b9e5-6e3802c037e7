-- Authentication and Subscription Setup Migration
-- Run this in Supabase SQL Editor after setting up Stripe

-- 1. Add subscription-related columns to auth.users (Supabase managed table)
-- Note: We'll use a separate subscriptions table instead of modifying auth.users directly

-- 2. Create subscriptions table for detailed subscription tracking
CREATE TABLE IF NOT EXISTS public.subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    stripe_subscription_id TEXT UNIQUE,
    stripe_customer_id TEXT NOT NULL,
    tier TEXT NOT NULL CHECK (tier IN ('starter', 'professional', 'enterprise')),
    status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'past_due', 'unpaid', 'trialing', 'incomplete')),
    current_period_start TIMESTAMPTZ NOT NULL,
    current_period_end TIMESTAMPTZ NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer_id ON public.subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON public.subscriptions(status);

-- Add trigger for updated_at
CREATE TRIGGER handle_updated_at_subscriptions 
    BEFORE UPDATE ON public.subscriptions 
    FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

-- 3. Create user_profiles table for additional user data
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT,
    avatar_url TEXT,
    subscription_tier TEXT DEFAULT 'starter' CHECK (subscription_tier IN ('starter', 'professional', 'enterprise')),
    subscription_status TEXT DEFAULT 'inactive' CHECK (subscription_status IN ('active', 'inactive', 'canceled', 'past_due', 'unpaid', 'trialing', 'incomplete')),
    onboarding_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Add trigger for updated_at
CREATE TRIGGER handle_updated_at_user_profiles 
    BEFORE UPDATE ON public.user_profiles 
    FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

-- 4. Create usage_tracking table for monitoring tier limits
CREATE TABLE IF NOT EXISTS public.usage_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    month_year TEXT NOT NULL, -- Format: 'YYYY-MM'
    api_requests_count INTEGER DEFAULT 0,
    configurations_count INTEGER DEFAULT 0,
    api_keys_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    UNIQUE(user_id, month_year)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_id ON public.usage_tracking(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_month_year ON public.usage_tracking(month_year);

-- Add trigger for updated_at
CREATE TRIGGER handle_updated_at_usage_tracking 
    BEFORE UPDATE ON public.usage_tracking 
    FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

-- 5. Enable RLS on new tables
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_tracking ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS policies for subscriptions
CREATE POLICY "Users can view their own subscription" ON public.subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all subscriptions" ON public.subscriptions
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- 7. Create RLS policies for user_profiles
CREATE POLICY "Users can view their own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 8. Create RLS policies for usage_tracking
CREATE POLICY "Users can view their own usage" ON public.usage_tracking
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all usage tracking" ON public.usage_tracking
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- 9. Update existing tables to enforce user relationships
-- Enable foreign key constraints that were commented out

-- For custom_api_configs
ALTER TABLE public.custom_api_configs 
ADD CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- For api_keys  
ALTER TABLE public.api_keys 
ADD CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- For request_logs
ALTER TABLE public.request_logs 
ADD CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;

-- 10. Enable RLS on existing tables
ALTER TABLE public.custom_api_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.request_logs ENABLE ROW LEVEL SECURITY;

-- 11. Create RLS policies for existing tables
-- Custom API Configs
CREATE POLICY "Users can manage their own configs" ON public.custom_api_configs
    FOR ALL USING (auth.uid() = user_id);

-- API Keys
CREATE POLICY "Users can manage their own API keys" ON public.api_keys
    FOR ALL USING (auth.uid() = user_id);

-- Request Logs
CREATE POLICY "Users can view their own request logs" ON public.request_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all request logs" ON public.request_logs
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- 12. Create function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile on user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 13. Create helper functions for subscription management
CREATE OR REPLACE FUNCTION public.get_user_subscription_tier(user_uuid UUID)
RETURNS TEXT AS $$
DECLARE
    tier_result TEXT;
BEGIN
    SELECT tier INTO tier_result
    FROM public.subscriptions
    WHERE user_id = user_uuid AND status = 'active'
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Default to starter if no active subscription found
    RETURN COALESCE(tier_result, 'starter');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 14. Create function to check tier limits
CREATE OR REPLACE FUNCTION public.check_tier_limits(
    user_uuid UUID,
    action_type TEXT,
    current_count INTEGER DEFAULT 0
)
RETURNS BOOLEAN AS $$
DECLARE
    user_tier TEXT;
    limit_value INTEGER;
BEGIN
    -- Get user's current tier
    SELECT public.get_user_subscription_tier(user_uuid) INTO user_tier;
    
    -- Set limits based on tier and action
    CASE 
        WHEN user_tier = 'starter' THEN
            CASE action_type
                WHEN 'configurations' THEN limit_value := 3;
                WHEN 'api_keys_per_config' THEN limit_value := 10;
                ELSE limit_value := 999999; -- Unlimited for other actions
            END CASE;
        WHEN user_tier = 'professional' THEN
            CASE action_type
                WHEN 'configurations' THEN limit_value := 15;
                WHEN 'api_keys_per_config' THEN limit_value := 50;
                ELSE limit_value := 999999; -- Unlimited for other actions
            END CASE;
        WHEN user_tier = 'enterprise' THEN
            limit_value := 999999; -- Unlimited for everything
        ELSE
            limit_value := 3; -- Default to starter limits
    END CASE;
    
    -- Return true if under limit, false if at or over limit
    RETURN current_count < limit_value;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

SELECT 'Authentication and subscription schema created successfully!' as result;
